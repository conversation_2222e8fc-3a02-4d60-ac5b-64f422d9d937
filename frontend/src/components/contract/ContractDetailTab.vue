<template>
  <div class="contract-detail-tab">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-icon class="loading-icon" :size="48">
        <Loading />
      </el-icon>
      <p>正在加载合同详情...</p>
    </div>

    <!-- 合同详情内容 -->
    <div v-else-if="contract" class="detail-content">
      <!-- 合同基本信息卡片 -->
      <el-card class="info-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <div class="header-title">
              <el-icon><Document /></el-icon>
              <span>合同信息</span>
            </div>
            <div class="header-actions">
              <el-tag :type="getStatusColor(contract.status)" size="large">
                {{ formatStatus(contract.status) }}
              </el-tag>
            </div>
          </div>
        </template>

        <div class="contract-info">
          <div class="info-item">
            <label class="info-label">流水号：</label>
            <span class="info-value">{{ contract.serial_number }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">文件名：</label>
            <span class="info-value">{{ contract.filename }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">文件大小：</label>
            <span class="info-value">{{
              formatFileSize(contract.file_size)
            }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">提交人：</label>
            <span class="info-value">{{ contract.submitter_name }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">审核人：</label>
            <span class="info-value">{{
              contract.reviewer_name || "未分配"
            }}</span>
          </div>

          <div class="info-item">
            <label class="info-label">提交时间：</label>
            <span class="info-value">{{
              formatDateTime(contract.created_at)
            }}</span>
          </div>

          <div class="info-item full-width">
            <label class="info-label">提交说明：</label>
            <span class="info-value">{{ contract.submit_note || "无" }}</span>
          </div>

          <!-- 审核时间 -->
          <div v-if="contract.reviewed_at" class="info-item">
            <label class="info-label">审核时间：</label>
            <span class="info-value">{{
              formatDateTime(contract.reviewed_at)
            }}</span>
          </div>
        </div>

        <!-- 审核历史区域 -->
        <div
          v-if="contract.reviewHistory && contract.reviewHistory.length > 0"
          class="review-history-section"
        >
          <h4 class="review-title">
            <el-icon><ChatDotRound /></el-icon>
            审核历史
          </h4>
          <div class="review-history">
            <div
              v-for="(review, index) in contract.reviewHistory"
              :key="review.id"
              class="review-item"
              :class="`review-item--${review.result}`"
            >
              <div class="review-header">
                <div class="reviewer-info">
                  <span class="reviewer-name">{{ review.reviewer_name }}</span>
                  <el-tag
                    :type="review.result === 'approved' ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ review.result === "approved" ? "通过" : "拒绝" }}
                  </el-tag>
                  <span class="review-level">
                    {{
                      review.review_level === "county_reviewer"
                        ? "县局审核"
                        : review.review_level === "city_reviewer"
                        ? "市局审核"
                        : review.review_level === "legal_officer"
                        ? "合同编写"
                        : "审核"
                    }}
                  </span>
                </div>
                <div class="review-time">
                  {{ formatDateTime(review.created_at) }}
                </div>
              </div>
              <div v-if="review.comment" class="review-comment">
                {{ review.comment }}
              </div>
            </div>
          </div>
        </div>

        <!-- 当前审核意见（兼容旧数据） -->
        <div v-else-if="contract.review_comment" class="review-section">
          <h4 class="review-title">
            <el-icon><ChatDotRound /></el-icon>
            审核意见
          </h4>
          <div
            class="review-comment"
            :class="`review-comment--${contract.status}`"
          >
            {{ contract.review_comment }}
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <el-button
            v-if="canModify(contract)"
            type="warning"
            @click="editContract"
          >
            <el-icon><Edit /></el-icon>
            修改合同
          </el-button>
        </div>
      </el-card>

      <!-- 审核操作面板 -->
      <el-card
        v-if="canReviewContract(contract, currentUser)"
        ref="reviewPanelRef"
        class="review-card"
      >
        <div class="panel-header">
          <h3 class="panel-title">
            <el-icon><EditPen /></el-icon>
            审核操作
          </h3>
        </div>

        <div class="review-form">
          <el-form
            ref="reviewFormRef"
            :model="reviewForm"
            :rules="reviewRules"
            label-width="80px"
          >
            <el-form-item label="审核结果" prop="result">
              <el-radio-group v-model="reviewForm.result" size="default">
                <el-radio value="approved" border>
                  <el-icon color="#67c23a"><CircleCheckFilled /></el-icon>
                  <span style="margin-left: 8px">通过</span>
                </el-radio>
                <el-radio value="rejected" border>
                  <el-icon color="#f56c6c"><CircleCloseFilled /></el-icon>
                  <span style="margin-left: 8px">拒绝</span>
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item label="审核意见" prop="comment">
              <el-input
                v-model="reviewForm.comment"
                type="textarea"
                :rows="4"
                placeholder="请输入审核意见（拒绝时至少10个字符）"
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                :loading="submittingReview"
                :disabled="
                  !reviewForm.result ||
                  (reviewForm.result === 'rejected' && !reviewForm.comment)
                "
                @click="submitReviewAction"
              >
                <el-icon><Check /></el-icon>
                提交审核结果
              </el-button>
              <el-button @click="resetReviewForm">
                <el-icon><RefreshLeft /></el-icon>
                重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- PDF 预览区域 - 独立显示在页面底部 -->
    <div v-if="contract && previewUrl" class="pdf-preview-section">
      <div class="preview-header">
        <div class="preview-title">
          <el-icon><View /></el-icon>
          <span>文件预览</span>
        </div>
      </div>

      <div class="preview-content">
        <SimplePDFViewer
          :src="previewUrl"
          :filename="contract.filename"
          @load="handlePreviewLoad"
          @error="handlePreviewError"
        />
      </div>
    </div>

    <!-- PDF 预览错误状态 -->
    <div v-else-if="contract && !previewUrl" class="pdf-preview-section">
      <div class="preview-header">
        <div class="preview-title">
          <el-icon><View /></el-icon>
          <span>文件预览</span>
        </div>
      </div>

      <div class="preview-placeholder">
        <el-icon :size="64" class="placeholder-icon">
          <DocumentCopy />
        </el-icon>
        <p class="placeholder-text">无法预览此文件</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else class="error-container">
      <el-icon :size="64" class="error-icon">
        <Warning />
      </el-icon>
      <p class="error-text">合同不存在或已被删除</p>
    </div>

    <!-- 编辑对话框 -->
    <ContractEditDialog
      v-model="showEditDialog"
      :contract="contract"
      @updated="handleContractUpdated"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  Loading,
  Document,
  Edit,
  Check,
  View,
  DocumentCopy,
  Warning,
  ChatDotRound,
  EditPen,
  CircleCheckFilled,
  CircleCloseFilled,
  RefreshLeft,
} from "@element-plus/icons-vue";

import SimplePDFViewer from "@/components/common/SimplePDFViewer.vue";
import ContractEditDialog from "@/components/contract/ContractEditDialog.vue";
import { useContracts } from "@/composables/useContracts";
import { useAuth } from "@/composables/useAuth";
import { filesAPI } from "@/api/files";
import { contractUtils } from "@/api/contracts";

// 定义 props
const props = defineProps({
  contractId: {
    type: [Number, String],
    required: true,
  },
  contract: {
    type: Object,
    default: null,
  },
});

// 定义 emits
const emit = defineEmits(["edit", "review", "updated"]);

// 认证信息
const { user: authUser } = useAuth();

// 临时解决方案：直接从localStorage获取用户信息
const currentUser = computed(() => {
  try {
    const savedUser = localStorage.getItem("user");
    return savedUser ? JSON.parse(savedUser) : null;
  } catch (error) {
    console.error("解析用户信息失败:", error);
    return null;
  }
});

// 合同管理
const {
  getContractDetail,
  canModify,
  canReview,
  formatStatus,
  getStatusColor,
  formatFileSize,
  formatDateTime,
  submitReview,
} = useContracts();

// 响应式数据
const loading = ref(false);
const contract = ref(props.contract);
const showEditDialog = ref(false);

// 审核相关状态
const submittingReview = ref(false);
const reviewFormRef = ref();
const reviewPanelRef = ref();

// 审核表单
const reviewForm = ref({
  result: "",
  comment: "",
});

// 动态审核表单验证规则
const reviewRules = computed(() => {
  const rules = {
    result: [{ required: true, message: "请选择审核结果", trigger: "change" }],
    comment: [],
  };

  // 根据审核结果动态设置comment验证规则
  if (reviewForm.value.result === "rejected") {
    // 审核驳回时，说明必填且至少10个字符
    rules.comment = [
      { required: true, message: "请填写审核意见", trigger: "blur" },
      { min: 10, message: "审核意见至少10个字符", trigger: "blur" },
      { max: 1000, message: "审核意见不能超过1000个字符", trigger: "blur" },
    ];
  } else if (reviewForm.value.result === "approved") {
    // 审核通过时，说明可选
    rules.comment = [
      { max: 1000, message: "审核意见不能超过1000个字符", trigger: "blur" },
    ];
  } else {
    // 未选择审核结果时，暂不验证comment
    rules.comment = [
      { max: 1000, message: "审核意见不能超过1000个字符", trigger: "blur" },
    ];
  }

  return rules;
});

// 开发环境标志
const isDev = computed(() => import.meta.env.DEV);

// 权限检查函数
const canReviewContract = (contract, user) => {
  return contractUtils.canReview(contract, user);
};

// 预览相关 - 使用不带token的URL，让SimplePDFViewer自动添加token
const previewUrl = computed(() => {
  if (contract.value && contract.value.id) {
    return filesAPI.getContractPreviewUrl(contract.value.id);
  }
  return null;
});

// 加载合同详情
const loadContractDetail = async () => {
  // 总是从API获取完整的合同详情，确保包含reviewHistory
  // 即使传递了contract prop，也要重新获取以确保数据完整性
  loading.value = true;
  try {
    const result = await getContractDetail(props.contractId);
    contract.value = result;
  } finally {
    loading.value = false;
  }
};

// 监听 props 变化
watch(
  () => props.contractId,
  (newId) => {
    if (newId) {
      loadContractDetail();
    }
  },
  { immediate: true },
);

watch(
  () => props.contract,
  (newContract) => {
    if (newContract && props.contractId) {
      // 当contract prop变化时，重新获取完整的合同详情
      loadContractDetail();
    }
  },
  { immediate: true },
);

// 编辑合同
const editContract = () => {
  showEditDialog.value = true;
};

// 处理合同更新
const handleContractUpdated = (updatedContract) => {
  contract.value = updatedContract;
  emit("updated", updatedContract);
  ElMessage.success("合同修改成功");
};

// 提交审核结果
const submitReviewAction = async () => {
  try {
    // 验证表单
    await reviewFormRef.value.validate();

    const resultText = reviewForm.value.result === "approved" ? "通过" : "拒绝";
    await ElMessageBox.confirm(
      `确定要${resultText}这个合同吗？提交后无法修改审核结果。`,
      "确认提交审核结果",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      },
    );

    submittingReview.value = true;
    const reviewData = {
      result: reviewForm.value.result,
      comment: reviewForm.value.comment,
    };

    const result = await submitReview(contract.value.id, reviewData);

    if (result) {
      contract.value = result;
      emit("updated", result);
      resetReviewForm();
      ElMessage.success(`审核${resultText}成功`);
    }
  } catch (error) {
    if (error !== "cancel") {
      if (import.meta.env.DEV) {
        console.error("提交审核失败:", error);
      }
    }
  } finally {
    submittingReview.value = false;
  }
};

// 重置审核表单
const resetReviewForm = () => {
  reviewForm.value = {
    result: "",
    comment: "",
  };
  reviewFormRef.value?.clearValidate();
};

// PDF预览事件处理
const handlePreviewLoad = () => {};

const handlePreviewError = (error) => {
  console.error("PDF预览加载失败:", error);
};

// 组件挂载时加载数据
onMounted(() => {
  if (props.contractId && !contract.value) {
    loadContractDetail();
  }
});
</script>

<style scoped>
.contract-detail-tab {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #909399;
}

.loading-icon {
  margin-bottom: 16px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.contract-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-item.full-width {
  grid-column: 1 / -1;
  flex-direction: column;
  align-items: flex-start;
}

.info-label {
  font-weight: 600;
  color: #606266;
  margin-right: 8px;
  min-width: 80px;
}

.info-value {
  color: #303133;
  word-break: break-all;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

/* 审核面板样式 */
.review-card {
  margin-top: 20px;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.panel-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.review-form {
  max-width: 600px;
}

.review-form .el-radio {
  margin-right: 20px;
  margin-bottom: 12px;
}

.review-form .el-radio__label {
  display: flex;
  align-items: center;
}

.review-form .el-form-item {
  margin-bottom: 20px;
}

.review-form .el-button {
  margin-right: 12px;
}

/* PDF 预览区域样式 */
.pdf-preview-section {
  margin-top: 24px;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.preview-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.preview-actions {
  display: flex;
  gap: 8px;
}

.preview-content {
  padding: 0;
  background: #fff;
}

.preview-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #909399;
  background: #fafafa;
}

.placeholder-icon {
  margin-bottom: 16px;
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #f56c6c;
}

.error-icon {
  margin-bottom: 16px;
}

.error-text {
  margin-bottom: 16px;
  font-size: 16px;
}

/* 审核历史区域 */
.review-history-section {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.review-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.review-history {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.review-item {
  background: white;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.review-item--approved {
  border-left: 3px solid #67c23a;
}

.review-item--rejected {
  border-left: 3px solid #f56c6c;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.reviewer-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.reviewer-name {
  font-weight: 600;
  color: #303133;
}

.review-level {
  font-size: 12px;
  color: #909399;
  background: #f4f4f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.review-time {
  font-size: 12px;
  color: #909399;
}

/* 审核意见区域（兼容旧数据） */
.review-section {
  margin-top: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.review-comment {
  padding: 12px;
  background: #fff;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
  line-height: 1.6;
  color: #606266;
  white-space: pre-wrap;
  word-break: break-word;
}

.review-comment--approved {
  border-left: 4px solid #67c23a;
  background: #f0f9ff;
}

.review-comment--rejected {
  border-left: 4px solid #f56c6c;
  background: #fef0f0;
}
</style>
