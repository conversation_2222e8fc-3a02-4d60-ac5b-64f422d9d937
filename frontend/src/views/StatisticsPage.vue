<!--
  审核统计页面
  显示合同审核的统计数据和图表
-->
<template>
  <div class="statistics-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">审核统计</h1>
        <p class="page-description">查看合同审核的统计数据和趋势分析</p>
      </div>
    </div>

    <!-- 法规员特殊提示 -->
    <div v-if="isLegalOfficer" class="legal-officer-notice">
      <el-card class="notice-card">
        <div class="notice-content">
          <el-icon class="notice-icon" :size="48" color="#409EFF">
            <InfoFilled />
          </el-icon>
          <div class="notice-text">
            <h3>法规员角色说明</h3>
            <p>您的角色是市局法规员，主要职责是为通过审核的合同分配合同编号。</p>
            <p>法规员专注于合同编号分配工作，不提供详细的工作统计数据。</p>
            <div class="notice-actions">
              <el-button type="primary" @click="$router.push('/contracts/pending-number')">
                <el-icon><DocumentAdd /></el-icon>
                查看待分配编号合同
              </el-button>
              <el-button @click="$router.push('/contracts/number-management')">
                <el-icon><Management /></el-icon>
                编号管理
              </el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 统计卡片 -->
    <div v-if="!isLegalOfficer" class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon total">
                <el-icon :size="24"><Document /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ totalContracts }}</div>
                <div class="stats-label">全部</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon reviewing">
                <el-icon :size="24"><View /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ reviewingContracts }}</div>
                <div class="stats-label">审核中</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon approved">
                <el-icon :size="24"><Check /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ approvedContracts }}</div>
                <div class="stats-label">已通过</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="stats-card">
            <div class="stats-content">
              <div class="stats-icon rejected">
                <el-icon :size="24"><Close /></el-icon>
              </div>
              <div class="stats-info">
                <div class="stats-number">{{ rejectedContracts }}</div>
                <div class="stats-label">已拒绝</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div v-if="!isLegalOfficer" class="charts-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>审核趋势</span>
              </div>
            </template>
            <div class="chart-container">
              <v-chart
                class="chart"
                :option="trendChartOption"
                :loading="chartsLoading"
                autoresize
              />
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>审核分布</span>
              </div>
            </template>
            <div class="chart-container">
              <v-chart
                class="chart"
                :option="distributionChartOption"
                :loading="chartsLoading"
                autoresize
              />
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细统计表格 -->
    <div v-if="!isLegalOfficer" class="detailed-stats">
      <el-card>
        <template #header>
          <div class="card-header">
            <span>详细统计</span>
            <el-button type="primary" size="small">
              <el-icon><Download /></el-icon>
              导出报告
            </el-button>
          </div>
        </template>

        <el-table :data="detailedStats" style="width: 100%">
          <el-table-column prop="period" label="时间段" width="120" />
          <el-table-column prop="total" label="全部" width="100" />
          <el-table-column prop="reviewing" label="审核中" width="100" />
          <el-table-column prop="approved" label="已通过" width="100" />
          <el-table-column prop="rejected" label="已拒绝" width="100" />
          <el-table-column prop="efficiency" label="审核效率" width="120">
            <template #default="scope">
              <el-tag :type="getEfficiencyType(scope.row.efficiency)">
                {{ scope.row.efficiency }}%
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="avgTime" label="平均用时" />
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import * as echarts from "echarts";
import VChart from "vue-echarts";
import {
  Document,
  Clock,
  View,
  Check,
  Close,
  DataAnalysis,
  PieChart,
  Download,
  InfoFilled,
  DocumentAdd,
  Management,
} from "@element-plus/icons-vue";
import { statisticsAPI } from "@/api/statistics";
import { ElMessage } from "element-plus";
import { useAuth } from "@/composables/useAuth";

// 将ECharts添加到全局作用域
window.echarts = echarts;

// 获取用户信息
const { userRole } = useAuth();

// 检查是否为法规员
const isLegalOfficer = computed(() => userRole.value === 'legal_officer');

// 响应式数据
const loading = ref(false);
const chartsLoading = ref(false);
const totalContracts = ref(0);
const reviewingContracts = ref(0);
const approvedContracts = ref(0);
const rejectedContracts = ref(0);

// 图表配置
const trendChartOption = ref(null);
const distributionChartOption = ref(null);

const detailedStats = ref([]);

// 加载统计数据
const loadStatistics = async () => {
  try {
    loading.value = true;
    chartsLoading.value = true;

    // 如果是法规员，显示特殊提示
    if (isLegalOfficer.value) {
      ElMessage.info('法规员专注于合同编号分配工作，不提供详细统计数据');
      loading.value = false;
      chartsLoading.value = false;
      return;
    }

    // 获取汇总数据
    const summaryResponse = await statisticsAPI.getSummary();
    if (summaryResponse.success) {
      const data = summaryResponse.data.overview;
      totalContracts.value = data.totalAssigned || data.totalContracts || 0;
      reviewingContracts.value = data.pending || data.pendingReview || 0;
      approvedContracts.value = data.approved || 0;
      rejectedContracts.value = data.rejected || 0;
    }

    // 获取趋势数据
    const trendsResponse = await statisticsAPI.getTrends();
    if (trendsResponse.success) {
      setupTrendChart(trendsResponse.data);
    }

    // 获取分布数据
    const distributionResponse = await statisticsAPI.getDistribution();
    if (distributionResponse.success) {
      setupDistributionChart(distributionResponse.data);
    }

    // 设置详细统计数据
    const weeklyAvgTime = summaryResponse.data.weeklyAvgTime || "暂无数据";
    const monthlyAvgTime = summaryResponse.data.monthlyAvgTime || "暂无数据";

    detailedStats.value = [
      {
        period: "本周",
        total: totalContracts.value,
        reviewing: reviewingContracts.value,
        approved: approvedContracts.value,
        rejected: rejectedContracts.value,
        efficiency:
          totalContracts.value > 0
            ? Math.round(
                ((approvedContracts.value + rejectedContracts.value) /
                  totalContracts.value) *
                  100,
              )
            : 0,
        avgTime: weeklyAvgTime,
      },
      {
        period: "本月",
        total: totalContracts.value,
        reviewing: reviewingContracts.value,
        approved: approvedContracts.value,
        rejected: rejectedContracts.value,
        efficiency:
          totalContracts.value > 0
            ? Math.round(
                ((approvedContracts.value + rejectedContracts.value) /
                  totalContracts.value) *
                  100,
              )
            : 0,
        avgTime: monthlyAvgTime,
      },
    ];
  } catch (error) {
    console.error("加载统计数据失败:", error);
    ElMessage.error("加载统计数据失败");
  } finally {
    loading.value = false;
    chartsLoading.value = false;
  }
};

// 配置趋势图表
const setupTrendChart = (data) => {
  console.log("📊 设置趋势图表，原始数据:", data);

  // 后端直接返回trends数组，不是包装在trends属性中
  const trendsArray = Array.isArray(data) ? data : (data.trends || []);
  console.log("📊 处理后的趋势数组:", trendsArray);

  const dates = trendsArray.map((item) => item.date || item.month || item.week);
  const totals = trendsArray.map((item) => item.total || 0);
  const completed = trendsArray.map((item) => item.approved || 0);

  console.log("📊 图表数据:", { dates, totals, completed });
  console.log("📊 dates详细:", dates);
  console.log("📊 totals详细:", totals);
  console.log("📊 completed详细:", completed);

  // 如果没有数据，显示空状态
  if (dates.length === 0) {
    trendChartOption.value = {
      title: {
        text: "审核趋势",
        left: "center",
        textStyle: {
          fontSize: 16,
          color: "#333",
        },
      },
      graphic: {
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '暂无数据',
          fontSize: 14,
          fill: '#999'
        }
      }
    };
    return;
  }

  // 创建完整的图表配置
  const chartConfig = {
    title: {
      text: "审核趋势",
      left: "center",
    },
    tooltip: {
      trigger: "axis",
    },
    legend: {
      data: ["总数", "已通过"],
      bottom: 10,
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "15%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      data: dates,
    },
    yAxis: {
      type: "value",
      minInterval: 1,
    },
    series: [
      {
        name: "总数",
        type: "line",
        data: totals,
        itemStyle: {
          color: "#409eff",
        },
      },
      {
        name: "已通过",
        type: "line",
        data: completed,
        itemStyle: {
          color: "#67c23a",
        },
      },
    ],
  };

  console.log("📊 最终图表配置:", chartConfig);
  console.log("📊 设置图表选项...");
  trendChartOption.value = chartConfig;

  // 强制触发响应式更新
  setTimeout(() => {
    console.log("📊 延迟检查图表实例...");
    const charts = document.querySelectorAll('.chart');
    charts.forEach((chart, index) => {
      if (chart.__echarts__) {
        console.log(`📊 图表 ${index} 实例存在，尝试重新渲染`);
        chart.__echarts__.resize();
      } else {
        console.log(`📊 图表 ${index} 实例不存在`);
      }
    });
  }, 1000);
};

// 配置分布图表
const setupDistributionChart = (data) => {
  console.log("🥧 设置分布图表，原始数据:", data);

  const distributionData = data.distribution || [
    { name: "审核中", value: reviewingContracts.value },
    { name: "已通过", value: approvedContracts.value },
    { name: "已拒绝", value: rejectedContracts.value },
  ];

  console.log("🥧 分布图表数据:", distributionData);

  // 过滤掉值为0的数据项，避免显示空的扇形
  const filteredData = distributionData.filter(item => item.value > 0);

  // 如果没有数据，显示空状态
  if (filteredData.length === 0) {
    distributionChartOption.value = {
      title: {
        text: "审核分布",
        left: "center",
        textStyle: {
          fontSize: 16,
          color: "#333",
        },
      },
      graphic: {
        type: 'text',
        left: 'center',
        top: 'middle',
        style: {
          text: '暂无数据',
          fontSize: 14,
          fill: '#999'
        }
      }
    };
    return;
  }

  // 创建完整的饼图配置
  const pieConfig = {
    title: {
      text: "审核分布",
      left: "center",
      top: 10,
    },
    tooltip: {
      trigger: "item",
      formatter: "{a} <br/>{b}: {c} ({d}%)",
    },
    legend: {
      orient: "horizontal",
      bottom: 5,
      left: "center",
      itemGap: 20,
      textStyle: {
        fontSize: 12,
      },
    },
    grid: {
      bottom: 60, // 为图例留出足够空间
    },
    series: [
      {
        name: "审核分布",
        type: "pie",
        radius: ["35%", "65%"],
        center: ["50%", "45%"], // 向上移动图表中心，为底部图例留空间
        data: filteredData,
        label: {
          show: true,
          formatter: "{b}: {c}",
          position: "outside",
          fontSize: 11,
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 5,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
    color: ["#e6a23c", "#67c23a", "#f56c6c"],
  };

  console.log("🥧 最终分布图表配置:", pieConfig);
  console.log("🥧 设置饼图选项...");
  distributionChartOption.value = pieConfig;
};

// 方法
const getEfficiencyType = (efficiency) => {
  if (efficiency >= 90) return "success";
  if (efficiency >= 80) return "warning";
  return "danger";
};

// 生命周期
onMounted(() => {
  loadStatistics();
});
</script>

<style scoped>
.statistics-page {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-description {
  color: #606266;
  margin: 0;
}

.stats-cards {
  margin-bottom: 20px;
}

.stats-card {
  height: 120px;
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
}

.stats-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.reviewing {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.approved {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-icon.rejected {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-number {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 4px;
}

.charts-section {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart {
  width: 100%;
  height: 100%;
}

.chart-placeholder {
  text-align: center;
  color: #909399;
}

.placeholder-text {
  font-size: 12px;
  margin-top: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detailed-stats {
  margin-bottom: 20px;
}

/* 法规员提示样式 */
.legal-officer-notice {
  margin-bottom: 20px;
}

.notice-card {
  border: 2px solid #409EFF;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.notice-content {
  display: flex;
  align-items: flex-start;
  gap: 20px;
  padding: 20px;
}

.notice-icon {
  flex-shrink: 0;
  margin-top: 10px;
}

.notice-text {
  flex: 1;
}

.notice-text h3 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
}

.notice-text p {
  margin: 0 0 8px 0;
  color: #606266;
  line-height: 1.6;
}

.notice-actions {
  margin-top: 20px;
  display: flex;
  gap: 12px;
}

.notice-actions .el-button {
  display: flex;
  align-items: center;
  gap: 6px;
}
</style>
