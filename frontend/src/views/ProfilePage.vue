<!--
  个人资料页面
  显示和编辑用户个人信息
-->
<template>
  <div class="profile-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="user-avatar">
          <!-- 使用专业的头像上传组件 -->
          <AvatarUploader
            v-model="currentAvatar"
            :avatar-size="100"
            :output-size="300"
            placeholder-text="点击上传头像"
            overlay-text="更换头像"
            @crop-complete="handleAvatarUpload"
            @file-select="handleFileSelect"
          />
        </div>
        <div class="user-info">
          <h1 class="user-name">
            {{ user?.username || userStore.user?.username || "用户" }}
          </h1>
          <p class="user-role">
            <el-tag :type="getRoleTagType(userRole || userStore.user?.role)">
              {{ getRoleDisplayName() || getRoleDisplayFromStore() }}
            </el-tag>
          </p>
          <p class="user-desc">{{ getUserDescription() }}</p>
        </div>
      </div>
    </div>

    <!-- 个人信息卡片 -->
    <div class="profile-content">
      <el-row :gutter="24">
        <!-- 基本信息 -->
        <el-col :span="12">
          <el-card class="info-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">
                  <i class="el-icon-user"></i>
                  基本信息
                </span>
                <el-button
                  type="link"
                  class="edit-btn"
                  @click="editMode = !editMode"
                >
                  {{ editMode ? "取消编辑" : "编辑信息" }}
                </el-button>
              </div>
            </template>

            <el-form
              ref="profileFormRef"
              :model="profileForm"
              :rules="profileRules"
              label-width="80px"
              class="profile-form"
            >
              <el-form-item label="用户名">
                <el-input
                  v-model="profileForm.username"
                  disabled
                  placeholder="用户名不可修改"
                />
              </el-form-item>

              <el-form-item label="真实姓名" prop="realName">
                <el-input
                  v-model="profileForm.realName"
                  :disabled="!editMode"
                  placeholder="请输入真实姓名"
                />
              </el-form-item>

              <el-form-item label="邮箱地址" prop="email">
                <el-input
                  v-model="profileForm.email"
                  :disabled="!editMode"
                  placeholder="请输入邮箱地址"
                />
              </el-form-item>

              <el-form-item label="手机号码" prop="phone">
                <el-input
                  v-model="profileForm.phone"
                  :disabled="!editMode"
                  placeholder="请输入手机号码"
                />
              </el-form-item>

              <el-form-item label="部门">
                <el-input
                  v-model="profileForm.department"
                  :disabled="!editMode"
                  placeholder="请输入所属部门"
                />
              </el-form-item>

              <el-form-item v-if="editMode">
                <el-button
                  type="primary"
                  :loading="loading"
                  @click="updateProfile"
                >
                  保存修改
                </el-button>
                <el-button @click="cancelEdit"> 取消 </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>

        <!-- 账户统计 -->
        <el-col :span="12">
          <el-card class="stats-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">
                  <i class="el-icon-data-line"></i>
                  {{ getStatsTitle() }}
                </span>
              </div>
            </template>

            <div class="stats-grid">
              <div
                v-for="stat in getStatsData()"
                :key="stat.key"
                class="stat-item"
              >
                <div class="stat-value">{{ stat.value }}</div>
                <div class="stat-label">{{ stat.label }}</div>
              </div>
            </div>
          </el-card>

          <!-- 最近活动 -->
          <el-card class="activity-card" style="margin-top: 20px">
            <template #header>
              <div class="card-header">
                <span class="card-title">
                  <i class="el-icon-clock"></i>
                  最近活动
                </span>
              </div>
            </template>

            <div class="activity-list">
              <div
                v-for="activity in recentActivities"
                :key="activity.id"
                class="activity-item"
              >
                <div class="activity-icon">
                  <i :class="getActivityIcon(activity.type)"></i>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-time">
                    {{ formatTime(activity.time) }}
                  </div>
                </div>
              </div>

              <div v-if="recentActivities.length === 0" class="no-activity">
                <i class="el-icon-info"></i>
                <span>暂无最近活动</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 安全设置 -->
      <el-row style="margin-top: 20px">
        <el-col :span="24">
          <el-card class="security-card">
            <template #header>
              <div class="card-header">
                <span class="card-title">
                  <i class="el-icon-lock"></i>
                  安全设置
                </span>
              </div>
            </template>

            <div class="security-items">
              <div class="security-item">
                <div class="security-info">
                  <div class="security-title">登录密码</div>
                  <div class="security-desc">
                    定期更换密码可以提高账户安全性
                  </div>
                </div>
                <el-button type="primary" @click="showPasswordDialog = true">
                  修改密码
                </el-button>
              </div>

              <div class="security-item">
                <div class="security-info">
                  <div class="security-title">最后登录</div>
                  <div class="security-desc">{{ lastLoginInfo }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 修改密码对话框 -->
    <el-dialog
      v-model="showPasswordDialog"
      title="修改密码"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="passwordFormRef"
        :model="passwordForm"
        :rules="passwordRules"
        label-width="100px"
      >
        <el-form-item label="当前密码" prop="currentPassword">
          <el-input
            v-model="passwordForm.currentPassword"
            type="password"
            placeholder="请输入当前密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="passwordForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showPasswordDialog = false">取消</el-button>
          <el-button type="primary" :loading="loading" @click="changePassword">
            确认修改
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from "vue";
import { ElMessage } from "element-plus";
import { User } from "@element-plus/icons-vue";
import { useAuth } from "@/composables/useAuth";
import { useUserStore } from "@/stores/user";
import AvatarUploader from "@/components/common/AvatarUploader.vue";
import { getAvatarUrl } from "@/utils/config";
import { dashboardAPI } from "@/api/dashboard";

// 使用认证状态
const {
  user,
  userRole,
  changePassword: authChangePassword,
  getRoleDisplayName,
} = useAuth();

// 使用用户store
const userStore = useUserStore();

// 响应式数据
const loading = ref(false);
const editMode = ref(false);
const showPasswordDialog = ref(false);
const userAvatar = ref("");
const currentAvatar = ref("");

// 监听userStore变化，更新头像显示
watch(
  () => userStore.user?.avatar,
  (newAvatar) => {
    if (newAvatar) {
      userAvatar.value = getAvatarUrl(newAvatar);
      currentAvatar.value = getAvatarUrl(newAvatar);
    } else {
      userAvatar.value = "";
      currentAvatar.value = "";
    }
  },
  { immediate: true },
);

// 头像文件选择处理
const handleFileSelect = (file) => {};

// 头像裁剪完成处理
const handleAvatarUpload = async (cropData) => {
  try {
    loading.value = true;

    const formData = new FormData();
    formData.append("avatar", cropData.file, "avatar.jpg");

    const response = await fetch("/api/users/avatar", {
      method: "POST",
      headers: {
        Authorization: `Bearer ${userStore.token}`,
      },
      body: formData,
    });

    const data = await response.json();

    if (response.ok && data.success) {
      userStore.updateAvatar(data.data.avatar);
      currentAvatar.value = getAvatarUrl(data.data.avatar);
      ElMessage.success("头像上传成功！");
    } else {
      throw new Error(data.message || "头像上传失败");
    }
  } catch (error) {
    ElMessage.error("头像上传失败: " + error.message);
  } finally {
    loading.value = false;
  }
};

// 个人信息表单
const profileForm = reactive({
  username: "",
  realName: "",
  email: "",
  phone: "",
  department: "",
});

// 原始数据备份
const originalProfileData = reactive({});

// 密码修改表单
const passwordForm = reactive({
  currentPassword: "",
  newPassword: "",
  confirmPassword: "",
});

// 用户统计数据
const userStats = reactive({
  // 员工统计
  totalContracts: 0,
  pendingContracts: 0,
  approvedContracts: 0,
  rejectedContracts: 0,
  // 审核员统计
  totalReviewed: 0,
  pendingReview: 0,
  approvedReview: 0,
  rejectedReview: 0,
});

// 最近活动
const recentActivities = ref([]);

// 表单引用
const profileFormRef = ref();
const passwordFormRef = ref();

// 表单验证规则
const profileRules = {
  realName: [{ required: true, message: "请输入真实姓名", trigger: "blur" }],
  email: [{ type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }],
  phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
};

const passwordRules = {
  currentPassword: [
    { required: true, message: "请输入当前密码", trigger: "blur" },
  ],
  newPassword: [
    { required: true, message: "请输入新密码", trigger: "blur" },
    { min: 6, message: "密码长度不能少于6位", trigger: "blur" },
  ],
  confirmPassword: [
    { required: true, message: "请确认新密码", trigger: "blur" },
    {
      validator: (rule, value, callback) => {
        if (value !== passwordForm.newPassword) {
          callback(new Error("两次输入的密码不一致"));
        } else {
          callback();
        }
      },
      trigger: "blur",
    },
  ],
};

// 计算属性
const getRoleTagType = (role) => {
  const typeMap = {
    admin: "danger",
    county_reviewer: "warning",
    city_reviewer: "warning",
    employee: "info",
  };
  return typeMap[role] || "info";
};

const getRoleDisplayFromStore = () => {
  if (!userStore.user?.role) return "";

  const roleMap = {
    admin: "管理员",
    county_reviewer: "县级审核员",
    city_reviewer: "市级审核员",
    employee: "员工",
  };

  return roleMap[userStore.user.role] || userStore.user.role;
};

const getUserDescription = () => {
  const currentRole = userRole.value || userStore.user?.role;
  const descMap = {
    admin: "系统管理员，拥有所有权限",
    county_reviewer: "县级审核员，负责审核县级合同",
    city_reviewer: "市级审核员，负责审核市级合同",
    employee: "普通员工，可以提交合同",
  };
  return descMap[currentRole] || "系统用户";
};

const lastLoginInfo = computed(() => {
  // 这里应该从用户数据中获取最后登录信息
  return "2024-01-15 09:30:00";
});

// 获取统计标题
const getStatsTitle = () => {
  const currentRole = userRole.value || userStore.user?.role;
  if (currentRole === "employee") {
    return "合同统计";
  } else if (
    currentRole === "county_reviewer" ||
    currentRole === "city_reviewer"
  ) {
    return "审核统计";
  } else if (currentRole === "admin") {
    return "系统统计";
  } else if (currentRole === "legal_officer") {
    return "角色信息";
  }
  return "账户统计";
};

// 获取统计数据
const getStatsData = () => {
  const currentRole = userRole.value || userStore.user?.role;

  if (currentRole === "employee") {
    return [
      { key: "total", value: userStats.totalContracts, label: "提交合同" },
      { key: "pending", value: userStats.pendingContracts, label: "待审核" },
      { key: "approved", value: userStats.approvedContracts, label: "已通过" },
      { key: "rejected", value: userStats.rejectedContracts, label: "已拒绝" },
    ];
  } else if (
    currentRole === "county_reviewer" ||
    currentRole === "city_reviewer"
  ) {
    return [
      { key: "totalReviewed", value: userStats.totalReviewed, label: "已审核" },
      { key: "pendingReview", value: userStats.pendingReview, label: "待审核" },
      {
        key: "approvedReview",
        value: userStats.approvedReview,
        label: "审核通过",
      },
      {
        key: "rejectedReview",
        value: userStats.rejectedReview,
        label: "审核拒绝",
      },
    ];
  } else if (currentRole === "legal_officer") {
    return [
      { key: "role", value: "市局法规员", label: "角色" },
      { key: "function", value: "合同编号分配", label: "主要职能" },
      { key: "note", value: "不提供统计", label: "统计说明" },
      { key: "focus", value: "功能导向", label: "工作重点" },
    ];
  } else if (currentRole === "admin") {
    return [
      {
        key: "totalContracts",
        value: userStats.totalContracts,
        label: "总合同数",
      },
      {
        key: "pendingContracts",
        value: userStats.pendingContracts,
        label: "待审核",
      },
      {
        key: "approvedContracts",
        value: userStats.approvedContracts,
        label: "已通过",
      },
      {
        key: "rejectedContracts",
        value: userStats.rejectedContracts,
        label: "已拒绝",
      },
    ];
  }

  return [];
};

const updateProfile = async () => {
  try {
    await profileFormRef.value.validate();
    loading.value = true;

    // 这里应该调用API更新个人信息
    // await userAPI.updateProfile(profileForm)

    ElMessage.success("个人信息更新成功");
    editMode.value = false;

    // 更新原始数据
    Object.assign(originalProfileData, profileForm);
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error("更新个人信息失败:", error);
    }
  } finally {
    loading.value = false;
  }
};

const cancelEdit = () => {
  // 恢复原始数据
  Object.assign(profileForm, originalProfileData);
  editMode.value = false;
};

const changePassword = async () => {
  try {
    await passwordFormRef.value.validate();
    loading.value = true;

    const success = await authChangePassword({
      currentPassword: passwordForm.currentPassword,
      newPassword: passwordForm.newPassword,
    });

    if (success) {
      showPasswordDialog.value = false;
      // 清空表单
      Object.assign(passwordForm, {
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });
    }
  } catch (error) {
    if (import.meta.env.DEV) {
      console.error("修改密码失败:", error);
    }
  } finally {
    loading.value = false;
  }
};

const getActivityIcon = (type) => {
  const iconMap = {
    submit: "el-icon-upload",
    review: "el-icon-view",
    approve: "el-icon-check",
    reject: "el-icon-close",
  };
  return iconMap[type] || "el-icon-info";
};

const formatTime = (time) => {
  // 简单的时间格式化
  return new Date(time).toLocaleString();
};

// 初始化数据
const initData = async () => {
  // 确保用户状态被正确初始化
  if (!user.value && !userStore.user) {
    await userStore.initAuth();
  }

  const currentUser = user.value || userStore.user;

  if (currentUser) {
    Object.assign(profileForm, {
      username: currentUser.username,
      realName: currentUser.realName || currentUser.real_name || "",
      email: currentUser.email || "",
      phone: currentUser.phone || "",
      department: currentUser.department || "",
    });

    // 初始化头像显示
    if (currentUser.avatar) {
      userAvatar.value = getAvatarUrl(currentUser.avatar);
    } else {
      userAvatar.value = "";
    }

    // 备份原始数据
    Object.assign(originalProfileData, profileForm);
  } else {
    // 如果没有用户数据，使用模拟数据用于演示
    const mockUser = {
      username: "张三",
      role: "admin",
      realName: "张三",
      email: "<EMAIL>",
      phone: "13800138000",
      department: "技术部",
    };

    Object.assign(profileForm, mockUser);
    Object.assign(originalProfileData, mockUser);

    // 临时设置用户数据用于显示
    if (!userStore.user) {
      userStore.setUser(mockUser);
    }
  }

  // 从API获取统计数据
  await loadUserStats();

  // 从API获取最近活动
  await loadRecentActivities();
};

// 加载用户统计数据
const loadUserStats = async () => {
  try {
    const response = await dashboardAPI.getUserStats();
    if (response.success) {
      Object.assign(userStats, response.data);
    }
  } catch (error) {
    console.error("获取用户统计数据失败:", error);
    // 如果API失败，使用默认值
    Object.assign(userStats, {
      totalContracts: 0,
      pendingContracts: 0,
      approvedContracts: 0,
      rejectedContracts: 0,
      totalReviewed: 0,
      pendingReview: 0,
      approvedReview: 0,
      rejectedReview: 0,
    });
  }
};

// 加载最近活动
const loadRecentActivities = async () => {
  try {
    const response = await dashboardAPI.getActivities(5);
    if (response.success) {
      recentActivities.value = response.data;
    }
  } catch (error) {
    console.error("获取最近活动失败:", error);
    // 如果API失败，使用空数组
    recentActivities.value = [];
  }
};

// 组件挂载时初始化
onMounted(async () => {
  await initData();
});
</script>

<style scoped>
.profile-page {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 160px;
  gap: 20px;
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin: 0;
  padding: 0;
}

.change-avatar-btn {
  display: block;
  margin-top: 10px;
  font-size: 12px;
}

.user-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 300px;
  margin-left: 20px;
}

.user-name {
  margin: 0 0 10px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1.3;
  position: relative;
}

.user-name::after {
  content: "";
  position: absolute;
  bottom: -3px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 2px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  border-radius: 1px;
}

.user-role {
  margin: 0 0 10px 0;
}

.user-desc {
  margin: 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
  opacity: 0.8;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-weight: 600;
  color: #303133;
}

.card-title i {
  margin-right: 8px;
  color: #409eff;
}

.edit-btn {
  padding: 0;
  font-size: 14px;
}

.profile-form {
  margin-top: 10px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.activity-list {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 14px;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #909399;
}

.no-activity {
  text-align: center;
  padding: 40px 0;
  color: #909399;
}

.no-activity i {
  font-size: 24px;
  margin-bottom: 8px;
  display: block;
}

.security-items {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.security-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.security-item:last-child {
  border-bottom: none;
}

.security-info {
  flex: 1;
}

.security-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.security-desc {
  font-size: 14px;
  color: #606266;
}

/* 可点击的头像上传区域 */
.avatar-upload-area {
  display: inline-block;
  cursor: pointer;
  transition: all 0.3s ease;
}

.avatar-upload-area:hover {
  transform: scale(1.05);
}

.avatar-container {
  position: relative;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 2px dashed #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: #f5f5f5;
  transition: all 0.3s ease;
}

.avatar-container:hover {
  border-color: #409eff;
  border-style: solid;
}

.avatar-container .avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.avatar-container .avatar-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  color: #999;
  font-size: 12px;
  text-align: center;
}

.avatar-hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  opacity: 0;
  transition: opacity 0.3s ease;
  font-size: 12px;
  pointer-events: none;
}

.avatar-container:hover .avatar-hover-overlay {
  opacity: 1;
}

.avatar-hover-overlay span {
  margin-top: 4px;
}

/* 个人资料页面头像布局优化 */
.profile-page .header-content {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #e9ecef;
}

.profile-page .user-avatar {
  position: relative;
}

/* 确保AvatarUploader组件在ProfilePage中正确显示 - 防止重叠 */
.profile-page .avatar-uploader {
  display: block !important;
  position: relative !important;
  z-index: 1 !important;
}

.profile-page .user-avatar .avatar-uploader .avatar-display {
  display: block !important;
  margin: 0 !important;
  padding: 0 !important;
  position: relative !important;
  transform: none !important;
}

.profile-page .user-avatar .avatar-uploader .avatar-display:hover {
  transform: none !important;
}

.profile-page .user-avatar .avatar-uploader .avatar-container {
  margin: 0 !important;
  padding: 0 !important;
  position: relative !important;
  z-index: 2 !important;
}

/* 防止头像悬停时的变形影响布局 */
.profile-page .avatar-container:hover {
  transform: none !important;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .header-content {
    padding: 20px;
    min-height: 180px;
    gap: 16px;
  }

  .user-name {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 16px;
  }

  .user-name {
    font-size: 18px;
  }
}
</style>
