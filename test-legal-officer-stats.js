#!/usr/bin/env node

/**
 * 测试法规员统计功能
 * 验证法规员角色不显示工作统计的功能是否正常
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

// 测试法规员登录和统计功能
async function testLegalOfficerStats() {
  try {
    console.log('🧪 开始测试法规员统计功能...\n');

    // 1. 法规员登录
    console.log('1️⃣ 测试法规员登录...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      username: 'fagui_demo',
      password: '123456'
    });

    if (!loginResponse.data.success) {
      throw new Error('法规员登录失败');
    }

    const token = loginResponse.data.data.token;
    const headers = { Authorization: `Bearer ${token}` };
    console.log('✅ 法规员登录成功');

    // 2. 测试dashboard统计
    console.log('\n2️⃣ 测试dashboard统计...');
    const dashboardResponse = await axios.get(`${BASE_URL}/dashboard/stats`, { headers });
    console.log('Dashboard统计响应:', JSON.stringify(dashboardResponse.data, null, 2));

    // 3. 测试合同统计
    console.log('\n3️⃣ 测试合同统计...');
    const contractStatsResponse = await axios.get(`${BASE_URL}/contracts/stats`, { headers });
    console.log('合同统计响应:', JSON.stringify(contractStatsResponse.data, null, 2));

    // 4. 测试统计页面数据
    console.log('\n4️⃣ 测试统计页面数据...');
    const statisticsSummaryResponse = await axios.get(`${BASE_URL}/statistics/summary`, { headers });
    console.log('统计汇总响应:', JSON.stringify(statisticsSummaryResponse.data, null, 2));

    // 5. 测试搜索统计
    console.log('\n5️⃣ 测试搜索统计...');
    const searchStatsResponse = await axios.get(`${BASE_URL}/search/stats`, { headers });
    console.log('搜索统计响应:', JSON.stringify(searchStatsResponse.data, null, 2));

    console.log('\n🎉 法规员统计功能测试完成！');
    console.log('✅ 法规员角色已正确配置为不显示详细工作统计');

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应数据:', error.response.data);
    }
  }
}

// 运行测试
if (require.main === module) {
  testLegalOfficerStats();
}

module.exports = { testLegalOfficerStats };
