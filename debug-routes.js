#!/usr/bin/env node

/**
 * 路由调试脚本
 * 用于检查Express路由注册情况和匹配逻辑
 */

const express = require('express');
const app = express();

// 模拟中间件
const mockAuth = (req, res, next) => {
  req.user = {
    id: 5,
    username: 'legal_officer',
    role: 'legal_officer',
    status: 'active'
  };
  next();
};

// 创建路由器
const router = express.Router();

// 按照实际代码的顺序注册路由
console.log('=== 注册路由 ===');

// 1. 根路径
router.get('/', mockAuth, (req, res) => {
  console.log('匹配到根路径 /');
  res.json({ route: '/', message: '合同列表' });
});

// 2. /stats
router.get('/stats', mockAuth, (req, res) => {
  console.log('匹配到 /stats');
  res.json({ route: '/stats', message: '统计信息' });
});

// 3. /pending
router.get('/pending', mockAuth, (req, res) => {
  console.log('匹配到 /pending');
  res.json({ route: '/pending', message: '待审核合同' });
});

// 4. /reviewed
router.get('/reviewed', mockAuth, (req, res) => {
  console.log('匹配到 /reviewed');
  res.json({ route: '/reviewed', message: '已审核合同' });
});

// 5. /reviewers
router.get('/reviewers', mockAuth, (req, res) => {
  console.log('匹配到 /reviewers');
  res.json({ route: '/reviewers', message: '审核员列表' });
});

// 6. /my
router.get('/my', mockAuth, (req, res) => {
  console.log('匹配到 /my');
  res.json({ route: '/my', message: '我的合同' });
});

// 7. /pending-number (应该在 /:id 之前)
router.get('/pending-number', mockAuth, (req, res) => {
  console.log('✅ 匹配到 /pending-number');
  res.json({ 
    route: '/pending-number', 
    message: '待分配编号的合同',
    query: req.query,
    user: req.user
  });
});

// 8. /:id (通用路由，应该在最后)
router.get('/:id', mockAuth, (req, res) => {
  console.log('匹配到 /:id，参数:', req.params.id);
  const contractId = parseInt(req.params.id);
  if (!contractId) {
    return res.status(400).json({ 
      success: false, 
      message: '无效的合同ID', 
      code: 400 
    });
  }
  res.json({ route: '/:id', contractId, message: '合同详情' });
});

// 注册路由到应用
app.use('/api/contracts', router);

// 启动测试服务器
const PORT = 3001;
app.listen(PORT, () => {
  console.log(`\n🚀 调试服务器启动在端口 ${PORT}`);
  console.log('测试URL:');
  console.log(`  http://localhost:${PORT}/api/contracts/pending-number?page=1&pageSize=10`);
  console.log(`  http://localhost:${PORT}/api/contracts/123`);
  console.log('\n按 Ctrl+C 停止服务器');
});

// 显示路由注册顺序
console.log('\n=== 路由注册顺序 ===');
console.log('1. GET /');
console.log('2. GET /stats');
console.log('3. GET /pending');
console.log('4. GET /reviewed');
console.log('5. GET /reviewers');
console.log('6. GET /my');
console.log('7. GET /pending-number  ← 应该匹配这个');
console.log('8. GET /:id            ← 不应该匹配这个');
