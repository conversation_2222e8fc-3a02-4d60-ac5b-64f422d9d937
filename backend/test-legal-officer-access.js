#!/usr/bin/env node

/**
 * 测试legal_officer角色文件访问权限修复效果
 * 验证legal_officer是否能够访问合同文件
 */

const { ContractModel, UserModel } = require('./src/utils/database');
const { UserPermissionModel } = require('./src/models/rbac');

// 模拟权限检查函数
async function checkFileAccessPermission(user, contract) {
  try {
    // 获取用户的RBAC角色信息
    const userRole = await UserPermissionModel.getUserRole(user.id);

    // 管理员可以访问所有文件
    if (userRole && userRole.name === 'admin') {
      return true;
    }

    // 合同提交人可以访问自己的文件
    if (contract.submitter_id === user.id) {
      return true;
    }

    // 检查用户是否有合同查看权限
    const hasReadPermission = await UserPermissionModel.userHasPermission(user.id, 'contract:read');
    if (!hasReadPermission) {
      return false;
    }

    // 法规员权限检查
    if (userRole && userRole.name === 'legal_officer') {
      // 法规员可以访问待分配编号的合同文件
      if (contract.status === 'pending_contract_number') {
        return true;
      }
      // 法规员可以访问已分配给自己的合同文件
      if (contract.legal_officer_id === user.id) {
        return true;
      }
      // 法规员可以访问已完成的合同文件（用于查看历史记录）
      if (contract.status === 'completed') {
        return true;
      }
    }

    // 兼容旧版本角色检查 - 法规员
    if (user.role === 'legal_officer') {
      // 法规员可以访问待分配编号的合同文件
      if (contract.status === 'pending_contract_number') {
        return true;
      }
      // 法规员可以访问已分配给自己的合同文件
      if (contract.legal_officer_id === user.id) {
        return true;
      }
      // 法规员可以访问已完成的合同文件（用于查看历史记录）
      if (contract.status === 'completed') {
        return true;
      }
    }

    return false;
  } catch (error) {
    console.error('检查文件访问权限错误:', error);
    return false;
  }
}

async function testLegalOfficerAccess() {
  try {
    console.log('🧪 开始测试legal_officer文件访问权限...');

    // 查找legal_officer用户
    const legalOfficers = await UserModel.findByRole('legal_officer');
    if (!legalOfficers || legalOfficers.length === 0) {
      console.log('❌ 未找到legal_officer角色用户');
      return;
    }

    const legalOfficer = legalOfficers[0];
    console.log(`✅ 找到legal_officer用户: ${legalOfficer.username} (ID: ${legalOfficer.id})`);

    // 查找合同ID为1的合同
    const contract = await ContractModel.findById(1);
    if (!contract) {
      console.log('❌ 未找到合同ID为1的合同');
      return;
    }

    console.log(`✅ 找到合同: ${contract.filename} (状态: ${contract.status})`);

    // 测试权限检查
    const hasAccess = await checkFileAccessPermission(legalOfficer, contract);
    
    console.log('\n📊 权限检查结果:');
    console.log(`- 用户角色: ${legalOfficer.role}`);
    console.log(`- 合同状态: ${contract.status}`);
    console.log(`- 合同提交人ID: ${contract.submitter_id}`);
    console.log(`- 法规员ID: ${contract.legal_officer_id || '未分配'}`);
    console.log(`- 访问权限: ${hasAccess ? '✅ 允许' : '❌ 拒绝'}`);

    // 检查用户的RBAC权限
    const userRole = await UserPermissionModel.getUserRole(legalOfficer.id);
    const hasReadPermission = await UserPermissionModel.userHasPermission(legalOfficer.id, 'contract:read');
    
    console.log('\n🔐 RBAC权限信息:');
    console.log(`- RBAC角色: ${userRole ? userRole.name : '未找到'}`);
    console.log(`- contract:read权限: ${hasReadPermission ? '✅ 有' : '❌ 无'}`);

    if (hasAccess) {
      console.log('\n🎉 测试通过！legal_officer可以访问合同文件');
    } else {
      console.log('\n⚠️  测试失败！legal_officer无法访问合同文件');
      
      // 提供修复建议
      console.log('\n💡 可能的原因:');
      if (!hasReadPermission) {
        console.log('- legal_officer缺少contract:read权限');
      }
      if (contract.status !== 'pending_contract_number' && contract.status !== 'completed' && contract.legal_officer_id !== legalOfficer.id) {
        console.log('- 合同状态不符合legal_officer访问条件');
        console.log(`  当前状态: ${contract.status}`);
        console.log('  需要状态: pending_contract_number 或 completed 或 已分配给该法规员');
      }
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
testLegalOfficerAccess().then(() => {
  console.log('\n✅ 测试完成');
  process.exit(0);
}).catch(error => {
  console.error('❌ 测试失败:', error);
  process.exit(1);
});
