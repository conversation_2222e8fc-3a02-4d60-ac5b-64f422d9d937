/**
 * 统计数据路由
 * 提供审核统计页面所需的聚合数据
 */

const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const { PermissionMiddleware } = require('../middleware/rbac');
const { ResponseUtils } = require('../utils/helpers');
const { HTTP_STATUS, RESPONSE_MESSAGES, USER_ROLES } = require('../utils/constants');
const { database, ContractModel, UserModel } = require('../utils/database');
// 缓存相关导入已移除，系统直接从数据库获取数据

const router = express.Router();

/**
 * 获取审核统计汇总数据
 * GET /api/statistics/summary
 */
router.get('/summary',
  authenticateToken,
  async (req, res) => {
    try {
      const userRole = req.user.role;
      const userId = req.user.id;

      // 根据用户角色获取不同的统计数据
      let summaryData = {};

      if (userRole === USER_ROLES.ADMIN) {
        summaryData = await getAdminStatisticsSummary();
      } else if ([USER_ROLES.COUNTY_REVIEWER, USER_ROLES.CITY_REVIEWER].includes(userRole)) {
        summaryData = await getReviewerStatisticsSummary(userId);
      } else if (userRole === USER_ROLES.EMPLOYEE) {
        summaryData = await getEmployeeStatisticsSummary(userId);
      } else if (userRole === USER_ROLES.LEGAL_OFFICER) {
        summaryData = await getLegalOfficerStatisticsSummary(userId);
      }

      res.json(ResponseUtils.success(summaryData));

    } catch (error) {
      console.error('获取统计汇总数据错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 获取审核趋势数据
 * GET /api/statistics/trends
 */
router.get('/trends',
  authenticateToken,
  async (req, res) => {
    try {
      const userRole = req.user.role;
      const userId = req.user.id;
      const period = req.query.period || 'month'; // day, week, month

      let trendsData = {};

      if (userRole === USER_ROLES.ADMIN) {
        trendsData = await getAdminTrendsData(period);
      } else if ([USER_ROLES.COUNTY_REVIEWER, USER_ROLES.CITY_REVIEWER].includes(userRole)) {
        trendsData = await getReviewerTrendsData(userId, period);
      } else if (userRole === USER_ROLES.EMPLOYEE) {
        trendsData = await getEmployeeTrendsData(userId, period);
      } else if (userRole === USER_ROLES.LEGAL_OFFICER) {
        trendsData = await getLegalOfficerTrendsData(userId, period);
      }

      res.json(ResponseUtils.success(trendsData));

    } catch (error) {
      console.error('获取趋势数据错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

/**
 * 获取审核分布数据
 * GET /api/statistics/distribution
 */
router.get('/distribution',
  authenticateToken,
  async (req, res) => {
    try {
      const userRole = req.user.role;
      const userId = req.user.id;

      let distributionData = {};

      if (userRole === USER_ROLES.ADMIN) {
        distributionData = await getAdminDistributionData();
      } else if ([USER_ROLES.COUNTY_REVIEWER, USER_ROLES.CITY_REVIEWER].includes(userRole)) {
        distributionData = await getReviewerDistributionData(userId);
      } else if (userRole === USER_ROLES.EMPLOYEE) {
        distributionData = await getEmployeeDistributionData(userId);
      } else if (userRole === USER_ROLES.LEGAL_OFFICER) {
        distributionData = await getLegalOfficerDistributionData(userId);
      }

      res.json(ResponseUtils.success(distributionData));

    } catch (error) {
      console.error('获取分布数据错误:', error);
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseUtils.error(RESPONSE_MESSAGES.INTERNAL_ERROR, HTTP_STATUS.INTERNAL_SERVER_ERROR)
      );
    }
  }
);

// ==================== 管理员统计函数 ====================

/**
 * 获取管理员统计汇总数据
 */
async function getAdminStatisticsSummary() {
  // 获取基础统计
  const contractStats = await ContractModel.getStats();
  const userStats = await getUserStatistics();

  // 获取时间维度统计
  const monthlyStats = await getMonthlyContractStats();
  const dailyStats = await getDailyContractStats(7); // 最近7天

  // 获取审核效率统计
  const reviewEfficiency = await getReviewEfficiencyStats();

  return {
    overview: {
      totalContracts: contractStats.total,
      pendingReview: contractStats.pending,
      approved: contractStats.approved,
      rejected: contractStats.rejected,
      totalUsers: userStats.total,
      activeReviewers: userStats.activeReviewers
    },
    trends: {
      monthly: monthlyStats,
      daily: dailyStats
    },
    efficiency: reviewEfficiency,
    distribution: {
      byStatus: contractStats,
      byReviewer: await getContractsByReviewer()
    }
  };
}

/**
 * 获取管理员趋势数据
 */
async function getAdminTrendsData(period) {
  let sql, params = [];

  switch (period) {
    case 'day':
      sql = `
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
          COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
        FROM contracts 
        WHERE created_at >= datetime('now', '-30 days')
        GROUP BY DATE(created_at)
        ORDER BY date DESC
      `;
      break;
    case 'week':
      sql = `
        SELECT 
          strftime('%Y-W%W', created_at) as week,
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
          COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
        FROM contracts 
        WHERE created_at >= datetime('now', '-12 weeks')
        GROUP BY strftime('%Y-W%W', created_at)
        ORDER BY week DESC
      `;
      break;
    default: // month
      sql = `
        SELECT 
          strftime('%Y-%m', created_at) as month,
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
          COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
        FROM contracts 
        WHERE created_at >= datetime('now', '-12 months')
        GROUP BY strftime('%Y-%m', created_at)
        ORDER BY month DESC
      `;
  }

  const results = await database.all(sql, params);
  return results.reverse(); // 按时间正序排列
}

/**
 * 获取管理员分布数据
 */
async function getAdminDistributionData() {
  // 按状态分布
  const statusDistribution = await ContractModel.getStats();

  // 按审核员分布
  const reviewerDistribution = await database.all(`
    SELECT 
      u.username as reviewer_name,
      COUNT(c.id) as contract_count,
      COUNT(CASE WHEN c.status = 'approved' THEN 1 END) as approved_count,
      COUNT(CASE WHEN c.status = 'rejected' THEN 1 END) as rejected_count
    FROM users u
    LEFT JOIN contracts c ON u.id = c.reviewer_id
    WHERE u.role = 'reviewer'
    GROUP BY u.id, u.username
    ORDER BY contract_count DESC
  `);

  // 按月份分布
  const monthlyDistribution = await database.all(`
    SELECT 
      strftime('%Y-%m', created_at) as month,
      COUNT(*) as count
    FROM contracts 
    WHERE created_at >= datetime('now', '-12 months')
    GROUP BY strftime('%Y-%m', created_at)
    ORDER BY month
  `);

  // 构建分布数据数组，用于饼图显示
  const distribution = [
    { name: '待审核', value: statusDistribution.pending || 0 },
    { name: '已通过', value: statusDistribution.approved || 0 },
    { name: '已拒绝', value: statusDistribution.rejected || 0 }
  ];

  return {
    byStatus: statusDistribution,
    byReviewer: reviewerDistribution,
    byMonth: monthlyDistribution,
    distribution: distribution
  };
}

// ==================== 审核员统计函数 ====================

/**
 * 获取审核员统计汇总数据
 */
async function getReviewerStatisticsSummary(userId) {
  // 使用基于审核历史的统计方法
  const contractStats = await ContractModel.getReviewerStats(userId);
  const monthlyStats = await getMonthlyContractStats({ reviewer_id: userId });
  const efficiency = await getReviewerEfficiency(userId);

  // 计算平均审核时间
  const weeklyAvgTime = await getAverageReviewTimeByPeriod(userId, 'week');
  const monthlyAvgTime = await getAverageReviewTimeByPeriod(userId, 'month');

  // 计算总的已完成审核数量（approved + rejected）
  const totalCompleted = contractStats.approved + contractStats.rejected;

  return {
    overview: {
      totalAssigned: contractStats.pending + contractStats.pending_city_review + totalCompleted,
      pending: contractStats.pending + contractStats.pending_city_review,
      approved: contractStats.approved,
      rejected: contractStats.rejected,
      approvalRate: totalCompleted > 0 ?
        ((contractStats.approved / totalCompleted) * 100).toFixed(1) : 0
    },
    trends: {
      monthly: monthlyStats
    },
    efficiency: efficiency,
    weeklyAvgTime: weeklyAvgTime,
    monthlyAvgTime: monthlyAvgTime
  };
}

/**
 * 获取审核员趋势数据
 */
async function getReviewerTrendsData(userId, period) {
  // 类似管理员趋势数据，但添加 reviewer_id 过滤条件
  let sql, params = [userId];

  switch (period) {
    case 'day':
      sql = `
        SELECT 
          DATE(updated_at) as date,
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
          COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
        FROM contracts 
        WHERE reviewer_id = ? AND updated_at >= datetime('now', '-30 days')
        GROUP BY DATE(updated_at)
        ORDER BY date DESC
      `;
      break;
    default: // month
      sql = `
        SELECT 
          strftime('%Y-%m', updated_at) as month,
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
          COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
        FROM contracts 
        WHERE reviewer_id = ? AND updated_at >= datetime('now', '-12 months')
        GROUP BY strftime('%Y-%m', updated_at)
        ORDER BY month DESC
      `;
  }

  const results = await database.all(sql, params);
  return results.reverse();
}

/**
 * 获取审核员分布数据
 */
async function getReviewerDistributionData(userId) {
  // 使用基于审核历史的统计方法
  const statusDistribution = await ContractModel.getReviewerStats(userId);

  // 获取审核员的月度审核分布（基于审核历史表）
  const monthlyDistribution = await database.all(`
    SELECT
      strftime('%Y-%m', cr.created_at) as month,
      COUNT(*) as count
    FROM contract_reviews cr
    WHERE cr.reviewer_id = ? AND cr.created_at >= datetime('now', '-12 months')
    GROUP BY strftime('%Y-%m', cr.created_at)
    ORDER BY month
  `, [userId]);

  // 构建分布数据数组，用于饼图显示
  const distribution = [
    { name: '待审核', value: (statusDistribution.pending || 0) + (statusDistribution.pending_city_review || 0) },
    { name: '已通过', value: statusDistribution.approved || 0 },
    { name: '已拒绝', value: statusDistribution.rejected || 0 }
  ];

  return {
    byStatus: statusDistribution,
    byMonth: monthlyDistribution,
    distribution: distribution
  };
}

// ==================== 员工统计函数 ====================

/**
 * 获取员工统计汇总数据
 */
async function getEmployeeStatisticsSummary(userId) {
  const contractStats = await ContractModel.getStats({ submitter_id: userId });
  const monthlyStats = await getMonthlyContractStats({ submitter_id: userId });

  return {
    overview: {
      totalSubmitted: contractStats.total,
      pending: contractStats.pending,
      approved: contractStats.approved,
      rejected: contractStats.rejected,
      approvalRate: contractStats.total > 0 ?
        ((contractStats.approved / contractStats.total) * 100).toFixed(1) : 0
    },
    trends: {
      monthly: monthlyStats
    }
  };
}

/**
 * 获取员工趋势数据
 */
async function getEmployeeTrendsData(userId, period) {
  let sql, params = [userId];

  switch (period) {
    case 'day':
      sql = `
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
          COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
        FROM contracts 
        WHERE submitter_id = ? AND created_at >= datetime('now', '-30 days')
        GROUP BY DATE(created_at)
        ORDER BY date DESC
      `;
      break;
    default: // month
      sql = `
        SELECT 
          strftime('%Y-%m', created_at) as month,
          COUNT(*) as total,
          COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
          COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
        FROM contracts 
        WHERE submitter_id = ? AND created_at >= datetime('now', '-12 months')
        GROUP BY strftime('%Y-%m', created_at)
        ORDER BY month DESC
      `;
  }

  const results = await database.all(sql, params);
  return results.reverse();
}

/**
 * 获取员工分布数据
 */
async function getEmployeeDistributionData(userId) {
  const statusDistribution = await ContractModel.getStats({ submitter_id: userId });

  const monthlyDistribution = await database.all(`
    SELECT 
      strftime('%Y-%m', created_at) as month,
      COUNT(*) as count
    FROM contracts 
    WHERE submitter_id = ? AND created_at >= datetime('now', '-12 months')
    GROUP BY strftime('%Y-%m', created_at)
    ORDER BY month
  `, [userId]);

  // 构建分布数据数组，用于饼图显示（员工视角不显示审核中状态）
  const distribution = [
    { name: '待审核', value: statusDistribution.pending || 0 },
    { name: '已通过', value: statusDistribution.approved || 0 },
    { name: '已拒绝', value: statusDistribution.rejected || 0 }
  ];

  return {
    byStatus: statusDistribution,
    byMonth: monthlyDistribution,
    distribution: distribution
  };
}

// ==================== 辅助函数 ====================

/**
 * 获取用户统计信息
 */
async function getUserStatistics() {
  const userStats = await database.all(`
    SELECT
      role,
      status,
      COUNT(*) as count
    FROM users
    GROUP BY role, status
  `);

  const total = userStats.reduce((sum, stat) => sum + stat.count, 0);
  const activeReviewers = userStats
    .filter(stat => stat.role === 'reviewer' && stat.status === 'active')
    .reduce((sum, stat) => sum + stat.count, 0);

  return {
    total,
    activeReviewers,
    details: userStats
  };
}

/**
 * 获取月度合同统计
 */
async function getMonthlyContractStats(filters = {}) {
  let sql = `
    SELECT
      strftime('%Y-%m', created_at) as month,
      COUNT(*) as total,
      COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
      COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected,
      COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending
    FROM contracts
    WHERE created_at >= datetime('now', '-12 months')
  `;

  const params = [];

  if (filters.submitter_id) {
    sql += ' AND submitter_id = ?';
    params.push(filters.submitter_id);
  }

  if (filters.reviewer_id) {
    sql += ' AND reviewer_id = ?';
    params.push(filters.reviewer_id);
  }

  sql += ' GROUP BY strftime(\'%Y-%m\', created_at) ORDER BY month';

  return await database.all(sql, params);
}

/**
 * 获取日度合同统计
 */
async function getDailyContractStats(days = 7) {
  const sql = `
    SELECT
      DATE(created_at) as date,
      COUNT(*) as total,
      COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved,
      COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected
    FROM contracts
    WHERE created_at >= datetime('now', '-${days} days')
    GROUP BY DATE(created_at)
    ORDER BY date
  `;

  return await database.all(sql);
}

/**
 * 获取审核效率统计
 */
async function getReviewEfficiencyStats() {
  const sql = `
    SELECT
      AVG(
        CASE
          WHEN status IN ('approved', 'rejected') AND updated_at IS NOT NULL
          THEN julianday(updated_at) - julianday(created_at)
          ELSE NULL
        END
      ) as avg_review_days,
      COUNT(CASE WHEN status IN ('approved', 'rejected') THEN 1 END) as completed_reviews,
      COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_reviews
    FROM contracts
  `;

  const result = await database.get(sql);

  return {
    averageReviewTime: result.avg_review_days ? Math.round(result.avg_review_days * 10) / 10 : 0,
    completedReviews: result.completed_reviews || 0,
    pendingReviews: result.pending_reviews || 0,
    inProgressReviews: result.in_progress_reviews || 0
  };
}

/**
 * 获取按审核员分组的合同统计
 */
async function getContractsByReviewer() {
  const sql = `
    SELECT
      u.username as reviewer_name,
      u.id as reviewer_id,
      COUNT(c.id) as total_contracts,
      COUNT(CASE WHEN c.status = 'approved' THEN 1 END) as approved,
      COUNT(CASE WHEN c.status = 'rejected' THEN 1 END) as rejected,
      COUNT(CASE WHEN c.status = 'pending' THEN 1 END) as pending
    FROM users u
    LEFT JOIN contracts c ON u.id = c.reviewer_id
    WHERE u.role = 'reviewer' AND u.status = 'active'
    GROUP BY u.id, u.username
    ORDER BY total_contracts DESC
  `;

  return await database.all(sql);
}

/**
 * 获取审核员效率统计
 */
async function getReviewerEfficiency(userId) {
  const sql = `
    SELECT
      AVG(
        CASE
          WHEN status IN ('approved', 'rejected') AND updated_at IS NOT NULL
          THEN julianday(updated_at) - julianday(created_at)
          ELSE NULL
        END
      ) as avg_review_days,
      COUNT(CASE WHEN status IN ('approved', 'rejected') THEN 1 END) as completed_reviews,
      COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_reviews,
      COUNT(CASE WHEN status = 'reviewing' THEN 1 END) as in_progress_reviews
    FROM contracts
    WHERE reviewer_id = ?
  `;

  const result = await database.get(sql, [userId]);

  return {
    averageReviewTime: result.avg_review_days ? Math.round(result.avg_review_days * 10) / 10 : 0,
    completedReviews: result.completed_reviews || 0,
    pendingReviews: result.pending_reviews || 0,
    inProgressReviews: result.in_progress_reviews || 0
  };
}

/**
 * 计算指定时间段的平均审核时间
 */
async function getAverageReviewTimeByPeriod(userId, period) {
  try {
    let dateCondition = '';

    if (period === 'week') {
      // 本周
      dateCondition = 'AND reviewed_at >= datetime(\'now\', \'weekday 0\', \'-7 days\')';
    } else if (period === 'month') {
      // 本月
      dateCondition = 'AND reviewed_at >= datetime(\'now\', \'start of month\')';
    }

    const sql = `
      SELECT
        AVG(JULIANDAY(reviewed_at) - JULIANDAY(created_at)) as avg_days
      FROM contracts
      WHERE reviewer_id = ?
        AND status IN ('approved', 'rejected')
        AND reviewed_at IS NOT NULL
        AND created_at IS NOT NULL
        ${dateCondition}
    `;

    const result = await database.get(sql, [userId]);

    if (result && result.avg_days !== null) {
      const avgDays = Math.round(result.avg_days * 10) / 10; // 保留一位小数
      return `${avgDays}天`;
    }

    return '暂无数据';
  } catch (error) {
    console.error(`计算${period}平均审核时间失败:`, error);
    return '暂无数据';
  }
}

// ==================== 法规员统计函数 ====================

/**
 * 获取法规员统计汇总数据
 */
async function getLegalOfficerStatisticsSummary(userId) {
  // 法规员不提供详细的工作统计，只提供基本的功能信息
  return {
    overview: {
      message: '法规员专注于合同编号分配工作',
      description: '法规员不需要查看详细的工作统计数据',
      role: '市局法规员',
      function: '为通过审核的合同分配合同编号'
    },
    note: '法规员角色不提供工作统计功能'
  };
}

/**
 * 获取法规员趋势数据
 */
async function getLegalOfficerTrendsData(userId, period) {
  // 法规员不提供趋势统计
  return {
    message: '法规员不提供趋势统计数据',
    note: '法规员专注于合同编号分配工作，不需要查看趋势统计'
  };
}

/**
 * 获取法规员分布数据
 */
async function getLegalOfficerDistributionData(userId) {
  // 法规员不提供分布统计
  return {
    message: '法规员不提供分布统计数据',
    note: '法规员专注于合同编号分配工作，不需要查看分布统计'
  };
}

module.exports = router;
