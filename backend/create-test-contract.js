/**
 * 创建测试合同数据
 * 用于测试法规员的合同编号分配功能
 */

const { ContractModel } = require('./src/utils/database');
const { CONTRACT_STATUS } = require('./src/utils/constants');

async function createTestContract() {
  try {
    console.log('🧪 开始创建测试合同...');

    // 创建一个待分配编号的合同
    const testContract = {
      serial_number: 'TEST-2025-001',
      filename: '测试合同-法规员功能验证.pdf',
      original_filename: '测试合同-法规员功能验证.pdf',
      file_path: '/uploads/test-contract.pdf',
      file_size: 1024000, // 1MB
      file_hash: 'test-hash-123456',
      submitter_id: 1, // 假设员工ID为1
      submitter_name: '测试员工',
      submit_note: '这是一个用于测试法规员合同编号分配功能的测试合同',
      status: CONTRACT_STATUS.PENDING_CONTRACT_NUMBER, // 待分配合同编号
      reviewer_id: 3, // 假设审核员ID为3
      reviewer_name: '测试审核员',
      reviewed_at: new Date().toISOString(),
      review_comment: '审核通过，可以分配合同编号',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const result = await ContractModel.create(testContract);
    console.log('✅ 测试合同创建成功:', {
      id: result.id,
      serial_number: result.serial_number,
      status: result.status,
      filename: result.filename
    });

    // 验证创建结果
    const contracts = await ContractModel.findAll();
    const pendingContracts = contracts.filter(c => c.status === CONTRACT_STATUS.PENDING_CONTRACT_NUMBER);
    console.log(`📊 当前待分配编号的合同数量: ${pendingContracts.length}`);

    console.log('🎉 测试数据创建完成！');

  } catch (error) {
    console.error('❌ 创建测试合同失败:', error);
  }
}

// 执行创建
createTestContract();
